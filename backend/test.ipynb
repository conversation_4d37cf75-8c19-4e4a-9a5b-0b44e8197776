from langgraph.checkpoint.memory import MemorySaver
from langgraph.prebuilt import create_react_agent
from langgraph.store.memory import InMemoryStore
from langgraph.utils.config import get_store
from langmem import create_manage_memory_tool, create_memory_manager
from pydantic import BaseModel
from typing import Optional

# ✅ 1. Define user profile schema
class UserProfile(BaseModel):
    name: Optional[str] = None
    language: Optional[str] = None
    timezone: Optional[str] = None
    age: Optional[int] = None
    academic_level: Optional[str] = None
    interests: Optional[list[dict]] = None
    career_goals: Optional[list[dict]] = None

# ✅ 2. Memory extraction manager
manager = create_memory_manager(
    "openai:gpt-4.1-mini",
    schemas=[UserProfile],
    instructions="Extract user profile (interests, academic level, goals, etc.) from conversation.",
    enable_inserts=False,
)

# ✅ 3. Dummy retriever tool
def retriever_tool(query: str) -> str:
    """Simulated retriever for courses"""
    knowledge_base = {
        "ai": "AI Course: Learn Machine Learning, Deep Learning, and NLP.",
        "web dev": "Web Development Bootcamp: HTML, CSS, JS, React, and Django.",
        "data science": "Data Science Specialization: Python, Pandas, ML models."
    }
    for key, val in knowledge_base.items():
        if key in query.lower():
            return val
    return "I couldn’t find an exact match. You can explore AI, Web Dev, or Data Science courses."

# ✅ 4. Prompt that merges memories
def prompt(state):
    store = get_store()
    memories = store.search(("memories",), query=state["messages"][-1].content)

    system_msg = f"""
You are a helpful AI assistant for Nepali students.

- Always use stored user profile info if available.
- If user asks for courses, check their interests first.
- If no relevant memory, retrieve general course suggestions.

## Stored Memories:
{memories}
"""
    return [{"role": "system", "content": system_msg}, *state["messages"]]

# ✅ 5. Setup memory store & checkpoint
store = InMemoryStore(
    index={
        "dims": 1536,
        "embed": "openai:text-embedding-3-small",
    }
)
checkpointer = MemorySaver()

# ✅ 6. Create the agent with memory & retriever tools
agent = create_react_agent(
    "openai:gpt-4.1-mini",
    prompt=prompt,
    tools=[
        create_manage_memory_tool(namespace=("memories",)),
        retriever_tool,  # Q&A from retriever
    ],
    store=store,
    checkpointer=checkpointer,
)

# ✅ 7. Simple chat loop
if __name__ == "__main__":
    print("🎓 Nepali EdTech AI Assistant\n(Type 'exit' to quit)\n")
    new_config={"configurable": {"thread_id": "thread-b"}}
    while True:
        user_input = input("You: ")
        if user_input.lower() in ["exit", "quit"]:
            print("👋 Goodbye!")
            break

        # Send input to agent
        result = agent.invoke(
    {"messages": [{"role": "user", "content": user_input}]},
    config=new_config,
)
        
        # Print AI response
        ai_reply = result["messages"][-1]
        print(f"AI: {ai_reply}\n")




agent.invoke(
{
        "messages": [
            {"role": "user", "content": "I jsut completed My SEE exams and looking to study science. I am interested in engineering and want to know about the best colleges in Nepal for engineering."}
        ]
    },
    # We will continue the conversation (thread-a) by using the config with
    # the same thread_id
    config=config,
)

agent.invoke(
{
        "messages": [
            {"role": "user", "content": "yes but i would need some preprartions for the entrance exams. Can you help me with that?"}
        ]
    },
    # We will continue the conversation (thread-a) by using the config with
    # the same thread_id
    config=config,
)

